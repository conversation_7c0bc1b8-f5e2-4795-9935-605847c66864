<template>
  <ZPage title="PublicActivityBonusTip组件测试" backgroundColor="#f5f5f5">
    <div class="test-container">
      <h2>PublicActivityBonusTip组件测试页面</h2>

      <!-- 使用示例：活动奖励弹窗 -->
      <PublicActivityBonusTip
        :show="showActivityBonus"
        :background-image="lightBgImg"
        :gift-image="giftImg"
        :title-image="congratulationsImg"
        :tip-text="activityTipText"
        :bonus-amount="activityBonusAmount"
        :date-text="activityDateText"
        @confirm="handleActivityConfirm"
      />

      <!-- 使用示例：注册奖励弹窗 -->
      <PublicActivityBonusTip
        :show="showRegisterBonus"
        :background-image="lightBgImg"
        :gift-image="giftImg"
        :title-image="congratulationsImg"
        :tip-text="registerTipText"
        :bonus-amount="registerBonusAmount"
        button-text="Claim Now"
        button-gradient="linear-gradient(180deg, #4CAF50 0%, #45A049 100%)"
        @confirm="handleRegisterConfirm"
      />

      <!-- 测试按钮 -->
      <div class="button-group">
        <button @click="showActivityBonus = true" class="test-btn primary">显示活动奖励</button>
        <button @click="showRegisterBonus = true" class="test-btn success">显示注册奖励</button>
        <button @click="showLargeAmountBonus" class="test-btn tertiary">显示大额奖励</button>
        <button @click="showCustomBonus" class="test-btn secondary">显示自定义奖励</button>
      </div>
    </div>
  </ZPage>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import PublicActivityBonusTip from "@/components/ZPopDialog/PublicActivityBonusTip.vue";

// 导入图片资源
import giftImg from "@/assets/images/popDialog/activityBonus-headGift.png";
import lightBgImg from "@/assets/images/popDialog/activityBonus-light.png";
import congratulationsImg from "@/assets/images/popDialog/activityBonus-congratulations.png";

// 弹窗显示状态
const showActivityBonus = ref(false);
const showRegisterBonus = ref(false);

// 活动奖励数据
const activityTipText = ref("You received a bonus in Summer Promotion:");
const activityBonusAmount = ref("1,500");
const activityDateText = ref("Reward date: 2024-08-15");

// 注册奖励数据
const registerTipText = ref("Welcome! Registration bonus:");
const registerBonusAmount = ref("500");

// 处理活动奖励确认
const handleActivityConfirm = (element: HTMLElement | null) => {
  console.log("Activity bonus confirmed", element);
  showActivityBonus.value = false;
  // 这里可以触发金币动画等
  // emit("start-coin-animation", element);
};

// 处理注册奖励确认
const handleRegisterConfirm = (element: HTMLElement | null) => {
  console.log("Register bonus confirmed", element);
  showRegisterBonus.value = false;
  // 这里可以触发金币动画等
  // emit("start-coin-animation", element);
};

// 显示大额奖励
const showLargeAmountBonus = () => {
  activityTipText.value = "Congratulations! VIP Bonus:";
  activityBonusAmount.value = "50,000";
  activityDateText.value = "Reward date: 2024-08-15";
  showActivityBonus.value = true;
};

// 显示自定义奖励
const showCustomBonus = () => {
  registerTipText.value = "Special Event Bonus:";
  registerBonusAmount.value = "2,888";
  showRegisterBonus.value = true;
};
</script>

<style scoped lang="scss">
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
  }

  .button-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 20px;

    .test-btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.2s ease;

      &.primary {
        background: linear-gradient(135deg, #ffb800 0%, #ff8a00 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 184, 0, 0.3);
        }
      }

      &.secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
      }

      &.tertiary {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
        }
      }

      &.success {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
        }
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .test-container {
    padding: 16px;

    .button-group {
      flex-direction: column;

      .test-btn {
        width: 100%;
      }
    }
  }
}
</style>
