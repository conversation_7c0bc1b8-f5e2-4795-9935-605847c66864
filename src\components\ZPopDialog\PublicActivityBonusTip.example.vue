<template>
  <div>
    <!-- 使用示例：活动奖励弹窗 -->
    <PublicActivityBonusTip
      :show="showActivityBonus"
      :background-image="lightBgImg"
      :gift-image="giftImg"
      :title-image="congratulationsImg"
      :tip-text="activityTipText"
      :bonus-amount="activityBonusAmount"
      :date-text="activityDateText"
      @confirm="handleActivityConfirm"
    />

    <!-- 使用示例：注册奖励弹窗 -->
    <PublicActivityBonusTip
      :show="showRegisterBonus"
      :background-image="lightBgImg"
      :gift-image="giftImg"
      :title-image="congratulationsImg"
      :tip-text="registerTipText"
      :bonus-amount="registerBonusAmount"
      button-text="Claim Now"
      button-gradient="linear-gradient(180deg, #4CAF50 0%, #45A049 100%)"
      @confirm="handleRegisterConfirm"
    />

    <!-- 测试按钮 -->
    <div style="padding: 20px;">
      <button @click="showActivityBonus = true">显示活动奖励</button>
      <button @click="showRegisterBonus = true">显示注册奖励</button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import PublicActivityBonusTip from "./PublicActivityBonusTip.vue";

// 导入图片资源
import giftImg from "@/assets/images/popDialog/activityBonus-headGift.png";
import lightBgImg from "@/assets/images/popDialog/activityBonus-light.png";
import congratulationsImg from "@/assets/images/popDialog/activityBonus-congratulations.png";

// 弹窗显示状态
const showActivityBonus = ref(false);
const showRegisterBonus = ref(false);

// 活动奖励数据
const activityTipText = ref("You received a bonus in Summer Promotion:");
const activityBonusAmount = ref("1,500");
const activityDateText = ref("Reward date: 2024-08-15");

// 注册奖励数据
const registerTipText = ref("Welcome! Registration bonus:");
const registerBonusAmount = ref("500");

// 处理活动奖励确认
const handleActivityConfirm = (element: HTMLElement | null) => {
  console.log("Activity bonus confirmed", element);
  showActivityBonus.value = false;
  // 这里可以触发金币动画等
  // emit("start-coin-animation", element);
};

// 处理注册奖励确认
const handleRegisterConfirm = (element: HTMLElement | null) => {
  console.log("Register bonus confirmed", element);
  showRegisterBonus.value = false;
  // 这里可以触发金币动画等
  // emit("start-coin-animation", element);
};
</script>

<style scoped>
button {
  margin: 10px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}
</style>
