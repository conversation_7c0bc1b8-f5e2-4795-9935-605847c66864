# PublicActivityBonusTip 组件

## 概述

`PublicActivityBonusTip` 是一个通用的奖励弹窗样式组件，用于显示各种类型的奖励信息（如活动奖励、注册奖励等）。该组件已重构为纯样式组件，所有文本内容和业务逻辑都通过 props 传入。

## Props

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `show` | `boolean` | ✅ | - | 控制弹窗显示/隐藏 |
| `backgroundImage` | `string` | ✅ | - | 弹窗背景图片 URL |
| `giftImage` | `string` | ✅ | - | 顶部礼盒图片 URL |
| `titleImage` | `string` | ✅ | - | 标题图片 URL |
| `tipText` | `string` | ✅ | - | 提示文本内容 |
| `bonusAmount` | `string` | ✅ | - | 奖励金额（已格式化的字符串） |
| `dateText` | `string` | ❌ | - | 日期文本（可选） |
| `buttonText` | `string` | ❌ | `"Done"` | 按钮文本 |
| `buttonGradient` | `string` | ❌ | `"linear-gradient(180deg, #FF1E35 20.59%, #FF916C 94.85%)"` | 按钮背景渐变 |
| `buttonBorderGradient` | `string` | ❌ | `"#FFDFBF"` | 按钮边框渐变 |
| `showButtonLight` | `boolean` | ❌ | `true` | 是否显示按钮光效 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `confirm` | `element: HTMLElement \| null` | 用户点击确认按钮时触发，返回按钮元素引用 |

## 使用示例

### 基础用法

```vue
<template>
  <PublicActivityBonusTip
    :show="showDialog"
    :background-image="backgroundImg"
    :gift-image="giftImg"
    :title-image="titleImg"
    tip-text="You received a bonus in Summer Promotion:"
    bonus-amount="1,500"
    date-text="Reward date: 2024-08-15"
    @confirm="handleConfirm"
  />
</template>

<script setup>
import PublicActivityBonusTip from '@/components/ZPopDialog/PublicActivityBonusTip.vue';
import backgroundImg from '@/assets/images/popDialog/activityBonus-light.png';
import giftImg from '@/assets/images/popDialog/activityBonus-headGift.png';
import titleImg from '@/assets/images/popDialog/activityBonus-congratulations.png';

const showDialog = ref(false);

const handleConfirm = (element) => {
  console.log('Confirmed!', element);
  showDialog.value = false;
  // 可以在这里触发金币动画等效果
};
</script>
```

### 自定义按钮样式

```vue
<template>
  <PublicActivityBonusTip
    :show="showDialog"
    :background-image="backgroundImg"
    :gift-image="giftImg"
    :title-image="titleImg"
    tip-text="Welcome! Registration bonus:"
    bonus-amount="500"
    button-text="Claim Now"
    button-gradient="linear-gradient(180deg, #4CAF50 0%, #45A049 100%)"
    button-border-gradient="#81C784"
    @confirm="handleConfirm"
  />
</template>
```

## 迁移指南

### 从旧版本迁移

如果你之前使用的是包含业务逻辑的版本，需要进行以下调整：

1. **移除 store 依赖**：不再需要传入 store 相关的状态
2. **准备数据**：在父组件中准备所有需要显示的数据
3. **处理事件**：在父组件中处理确认事件和后续逻辑

### 迁移示例

**旧用法：**
```vue
<!-- 旧版本会自动从 store 获取数据 -->
<PublicActivityBonusTip />
```

**新用法：**
```vue
<template>
  <PublicActivityBonusTip
    :show="showActivityBonusTip"
    :background-image="lightBgImg"
    :gift-image="giftImg"
    :title-image="congratulationsImg"
    :tip-text="promotionText"
    :bonus-amount="formattedBonusAmount"
    :date-text="rewardDateText"
    @confirm="handleActivityConfirm"
  />
</template>

<script setup>
// 在父组件中处理数据和业务逻辑
const { showActivityBonusTip, popActivityBonus } = storeToRefs(autoPopMgrStore);

const promotionText = computed(() => {
  // 根据业务逻辑计算提示文本
});

const formattedBonusAmount = computed(() => {
  // 格式化金额显示
});

const handleActivityConfirm = async (element) => {
  // 处理确认逻辑，如发送 API 请求
  // 触发动画效果
  // 更新 store 状态
};
</script>
```

## 注意事项

1. **图片资源**：确保传入的图片 URL 是有效的，建议使用 import 导入的图片资源
2. **金额格式化**：`bonusAmount` 应该是已经格式化好的字符串（如 "1,500"）
3. **事件处理**：确认事件会返回按钮元素引用，可用于触发动画效果
4. **样式定制**：如需修改样式，可以通过 CSS 变量或者创建新的样式组件

## 样式说明

组件保留了完整的样式定义，包括：
- 弹窗背景和布局
- 礼盒图片定位
- 内容区域样式
- 金额显示样式
- 按钮样式
- 日期文本样式

所有样式都是 scoped 的，不会影响其他组件。
