<template>
  <!-- 通用奖励弹窗：反水奖励、注册奖励等 -->
  <ZPopOverlay :show="showDialog">
    <div class="wrap" :style="{ backgroundImage: `url(${lightBgImg})` }">
      <!-- 顶部礼盒图片 -->
      <img class="head-gift" :src="giftImg" alt="" />
      <!-- 内容外框-border显示 -->
      <div class="content-border">
        <!-- 内容 -->
        <div class="content">
          <!-- 标题 -->
          <img class="head-title" :src="congratulationsImg" alt="" />
          <div>
            <!-- 提示文本 -->
            <div class="tips">{{ promotionText }}</div>
            <!-- 金额 -->
            <div class="bonus-wrap">
              <IconCoin class="icon" :size="40" />
              <span class="bonus">{{ formattedBonusAmount }}</span>
            </div>
          </div>
          <div class="btn" ref="confirmBtnRef">
            <GradientButton
              background-gradient="linear-gradient(180deg, #FF1E35 20.59%, #FF916C 94.85%)"
              border-gradient="#FFDFBF"
              :showLight="true"
              @click="handleClick"
              >Done</GradientButton
            >
          </div>
          <div class="date" v-if="rewardDateText">{{ rewardDateText }}</div>
        </div>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { init_enum_typestr } from "@/utils/GlobalScript";
import { AWARD_UPDATE_TYPE, AWARD_NAME } from "@/utils/config/GlobalConstant";
import {
  getAdjustmentList,
  postActivityBonusReceive,
  postAdjustmentBonusRecordReceive,
} from "@/api/activity";
import { useGlobalStore } from "@/stores/global";
import { getToken } from "@/utils/auth";
// 导入图片资源，确保与预加载使用相同的路径
import giftImg from "@/assets/images/popDialog/activityBonus-headGift.png";
import lightBgImg from "@/assets/images/popDialog/activityBonus-light.png";
import congratulationsImg from "@/assets/images/popDialog/activityBonus-congratulations.png";

const globalStore = useGlobalStore();
const autoPopMgrStore = useAutoPopMgrStore();
const { showActivityBonusTip, showRegisterBonusTip, popActivityBonus, activityBonusType } =
  storeToRefs(autoPopMgrStore);
const { registerAward } = storeToRefs(globalStore);
// 使用更具语义的变量名
const promotionText = ref(""); // 奖励描述文字
const bonusAmountText = ref(""); // 奖励金额文本
const rewardDateText = ref(""); // 奖励日期文本

// 注册奖励相关变量（来自 RegisterBonusTip）
const registerTitle = ref("");
const registerCoin = ref("");

const emit = defineEmits(["start-coin-animation"]);
const confirmBtnRef = ref<HTMLElement | null>(null);

// 标记是否正在发送领奖请求
const isSending = ref(false);

// 判断当前显示的弹窗类型
const isRegisterBonus = computed(() => showRegisterBonusTip.value);
const isActivityBonus = computed(() => showActivityBonusTip.value);
const showDialog = computed(() => isRegisterBonus.value || isActivityBonus.value);

// 格式化金额，根据弹窗类型使用不同的数据源
const formattedBonusAmount = computed(() => {
  if (isRegisterBonus.value) {
    const num = Number(registerCoin.value?.replace("-", ""));
    return num >= 0 ? num.toLocaleString() : "--";
  } else {
    const num = Number(bonusAmountText.value?.replace("-", ""));
    return num >= 0 ? num.toLocaleString() : "--";
  }
});

// 关闭活动奖励弹窗，清理后调用余额刷新和销毁当前弹窗
const closeActivityTips = (bGoldAni?: boolean) => {
  // 移除本次奖励，可用 shift 删除首个数据
  autoPopMgrStore.popActivityBonus?.shift();
  showActivityBonusTip.value = false;
  if (bGoldAni) {
    emit("start-coin-animation", confirmBtnRef.value);
  }
  setTimeout(() => {
    if (autoPopMgrStore.popActivityBonus.length > 0) {
      showActivityBonusTip.value = true;
    } else {
      AutoPopMgr.destroyCurrentPopup();
    }
  }, 2000);
};

// 关闭注册奖励弹窗（来自 RegisterBonusTip）
const closeRegisterTips = () => {
  showRegisterBonusTip.value = false;
  emit("start-coin-animation", confirmBtnRef.value);
  // 防止重复弹框
  globalStore.updateRegisterAward({ type: 0 });
  // Balance 组件内部会更新余额
  setTimeout(() => {
    AutoPopMgr.destroyCurrentPopup();
  }, 2000);
};

// 统一的点击处理函数
const handleClick = async () => {
  if (isRegisterBonus.value) {
    // 注册奖励逻辑
    closeRegisterTips();
  } else if (isActivityBonus.value) {
    // 活动奖励逻辑
    if (!getToken()) return;
    if (isSending.value) return;
    isSending.value = true;
    const firstBonus = popActivityBonus.value[0] as any;
    const params = {
      type: firstBonus?.type || "",
      id: firstBonus.id,
    };
    // 根据 activity_name 判断使用哪一个 API
    const apiFn = firstBonus?.activity_name
      ? postAdjustmentBonusRecordReceive
      : postActivityBonusReceive;
    try {
      await apiFn(params);
      closeActivityTips(true);
    } catch (error) {
      closeActivityTips();
    } finally {
      isSending.value = false;
    }
  }
};

// 初始化注册奖励数据（来自 RegisterBonusTip）
const initRegisterBonusData = async () => {
  let updateType = activityBonusType.value;
  // 如果是注册奖励，根据不同 updateType 填写对应标题及金额
  const amount = registerAward.value.amount;
  registerTitle.value =
    updateType === AWARD_UPDATE_TYPE.REGISTER_USER
      ? AWARD_NAME.REGISTER_USER
      : AWARD_NAME.BING_IPHONE_USER;
  registerCoin.value = amount + "";
  promotionText.value = registerTitle.value;
  rewardDateText.value = ""; // 注册奖励不显示日期
};

// 初始化调账领奖数据
const initBonusRecordData = () => {
  const firstBonus = popActivityBonus.value[0] as any;
  const bonusTypeStr = firstBonus?.activity_name;
  promotionText.value = `You received a bonus in ${bonusTypeStr} promotion: `;
  bonusAmountText.value = firstBonus?.bonus ? `${firstBonus.bonus}` : "0";
  rewardDateText.value = `Reward date: ${firstBonus?.bonus_date || "--"}`;
};

// 获取并返回调账列表数据
const fetchAdjustmentList = async () => {
  try {
    return await getAdjustmentList({});
  } catch (error) {
    return [];
  }
};

// 初始化普通奖励数据（非调账奖励）
const initNormalBonusData = async () => {
  const firstBonus = popActivityBonus.value[0] as any;
  let updateType = firstBonus?.type;
  // 优先获取调账相关枚举数据
  let adjustmentEnum = await fetchAdjustmentList();
  let typeStr = "";
  if (adjustmentEnum && adjustmentEnum.length > 0) {
    const targetType = adjustmentEnum.filter((ele: any) => ele.change_type == updateType);
    if (targetType.length > 0) {
      typeStr = targetType[0].title;
      if (!typeStr) {
        init_enum_typestr(updateType, typeStr, AWARD_NAME.FREE_REGISTRATION);
      }
      promotionText.value = `You received a bonus in ${typeStr} promotion: `;
    }
  } else {
    init_enum_typestr(updateType, typeStr, AWARD_NAME.FREE_REGISTRATION);
    promotionText.value = `You received a bonus in ${typeStr} promotion: `;
  }
  bonusAmountText.value = firstBonus?.bonus ? `${firstBonus.bonus}` : `0`;
  rewardDateText.value = `Reward date: ${firstBonus?.bonus_date || "--"}`;
};

// 总初始化函数，根据奖励数据类型进行区分处理
const initBonusData = async () => {
  if (isRegisterBonus.value) {
    // 注册奖励初始化
    await initRegisterBonusData();
  } else if (isActivityBonus.value) {
    // 活动奖励初始化
    const firstBonus = popActivityBonus.value[0] as any;
    if (firstBonus?.activity_name && firstBonus?.id) {
      // 如果是调账奖励，则初始化调账数据
      initBonusRecordData();
    } else {
      await initNormalBonusData();
    }
  }
};

// 监听弹窗显示和奖励数据的变化，初始化数据
watch(
  [
    () => autoPopMgrStore.showActivityBonusTip,
    () => autoPopMgrStore.showRegisterBonusTip,
    () => autoPopMgrStore.popActivityBonus,
    () => autoPopMgrStore.activityBonusType,
  ],
  ([showActivity, showRegister, bonusList]) => {
    if (showRegister) {
      initBonusData();
    } else if (showActivity && bonusList?.length > 0) {
      initBonusData();
    }
  }
);

defineExpose({
  confirmBtnRef,
});
</script>

<style lang="scss" scoped>
.wrap {
  width: 100%;
  text-align: center;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  position: relative;
  font-family: "Inter";
  .head-gift {
    width: 160px;
    height: auto;
    margin: 0 auto;
    position: absolute;
    top: 95px;
    left: 30%;
  }
  .head-title {
    width: 220px;
    height: auto;
    margin: 0 auto 20px;
  }
  .content-border {
    background: #fff6c6;
    padding: 2px;
    margin: 150px 24px 90px;
    // min-height: 300px;
    border-radius: 33px;
    .content {
      border-radius: 33px;
      padding: 73px 21px 25px;
      background: linear-gradient(180deg, #ffd0d0cb 0%, #fff 30%);
      height: 100%;
      // min-height: 298px;
      width: 100%;
    }
  }
  .tips {
    color: #222;
    text-align: left;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;
  }
  .bonus-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    margin-top: 16px;
    line-height: 1;
    .bonus {
      color: #222;
      font-family: "D-DIN";
      font-size: 36px;
      font-style: normal;
      font-weight: 700;
    }
  }

  .btn {
    margin-top: 36px;
  }
  .date {
    margin-top: 8px;
    color: #222;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    line-height: normal;
  }
}
</style>
