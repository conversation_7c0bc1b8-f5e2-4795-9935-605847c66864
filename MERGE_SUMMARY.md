# 奖励弹窗组件合并总结

## 概述
已成功将 `RegisterBonusTip.vue` 和 `ActivityBonusTip.vue` 两个文件的逻辑代码合并到 `PublicActivityBonusTip.vue` 中，保持原有样式不变，仅改变展示文本内容等逻辑。

## 合并的文件
1. **源文件1**: `src/components/ZPopDialog/RegisterBonusTip.vue` - 注册奖励弹窗
2. **源文件2**: `src/components/ZPopDialog/ActivityBonusTip.vue` - 活动奖励弹窗  
3. **目标文件**: `src/components/ZPopDialog/PublicActivityBonusTip.vue` - 合并后的通用奖励弹窗

## 主要变更

### 1. 模板 (Template) 变更
- 将弹窗显示条件从 `showActivityBonusTip` 改为 `showDialog` 计算属性
- 为按钮添加了 `ref="confirmBtnRef"` 引用
- 为日期显示添加了条件渲染 `v-if="rewardDateText"`

### 2. 脚本 (Script) 变更

#### 导入和Store
- 添加了 `AWARD_UPDATE_TYPE` 常量导入
- 添加了 `showRegisterBonusTip`, `activityBonusType`, `registerAward` 等store引用

#### 新增变量
- `registerTitle` - 注册奖励标题
- `registerCoin` - 注册奖励金额
- `isRegisterBonus` - 判断是否为注册奖励
- `isActivityBonus` - 判断是否为活动奖励
- `showDialog` - 统一的弹窗显示状态

#### 逻辑合并
1. **格式化金额**: 根据弹窗类型使用不同的数据源
2. **关闭弹窗**: 分别处理注册奖励和活动奖励的关闭逻辑
3. **点击处理**: 统一的 `handleClick` 函数，根据弹窗类型执行不同逻辑
4. **数据初始化**: 
   - `initRegisterBonusData` - 注册奖励数据初始化
   - `initBonusRecordData` - 调账奖励数据初始化  
   - `initNormalBonusData` - 普通奖励数据初始化
   - `initBonusData` - 统一初始化函数

#### 监听器更新
- 监听 `showActivityBonusTip`, `showRegisterBonusTip`, `popActivityBonus`, `activityBonusType` 的变化
- 根据不同的弹窗类型触发相应的初始化逻辑

### 3. 功能特性

#### 注册奖励功能 (来自 RegisterBonusTip)
- 支持新用户注册奖励 (`REGISTER_USER`)
- 支持绑定手机号奖励 (`BING_IPHONE_USER`)
- 自动更新 `registerAward` 状态防止重复弹框
- 不显示奖励日期

#### 活动奖励功能 (来自 ActivityBonusTip)
- 支持普通活动奖励
- 支持调账奖励 (有 `activity_name` 的奖励)
- 支持奖励队列，可连续显示多个奖励
- 显示奖励日期
- 发送API请求领取奖励

## 测试更新
更新了 `src/views/test/PublicActivityBonusTipTest.vue` 测试文件：
- 添加了注册奖励和绑定手机奖励的测试按钮
- 更新了状态显示，分别显示两种弹窗的状态
- 添加了注册奖励相关的清理逻辑
- 新增了 success 样式类

## 兼容性
- 保持了原有的样式和UI不变
- 保持了原有的API调用逻辑
- 保持了原有的动画和交互效果
- 向后兼容现有的使用方式

## 使用方式
合并后的组件可以同时处理：
1. 注册奖励弹窗 - 通过 `showRegisterBonusTip` 控制
2. 活动奖励弹窗 - 通过 `showActivityBonusTip` 控制

组件会自动根据当前显示的弹窗类型选择相应的逻辑和数据源。
